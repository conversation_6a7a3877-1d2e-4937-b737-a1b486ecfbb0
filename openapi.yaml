openapi: "3.0.0"
info:
  title: "E-Invoice API"
  version: "1.0.0"
  description: ""
components:
  responses:
    Forbidden:
      description: "Access token is missing or invalid"
    Accepted:
      description: "The request was accepted"
    Created:
      description: "The resource has been created"
    NotFound:
      description: "The resource has been created"
    NotAcceptable:
      description: "The resource has been created"
  securitySchemes:
    BearerAuth:
      type: "http"
      scheme: "bearer"
    BasicAuth:
      type: "http"
      scheme: "basic"
    ApiKeyAuth:
      type: "apiKey"
      in: "header"
      name: "X-API-Key"
  schemas:
    Any:
      description: "Any JSON object not defined as schema"
    .DS_Store:
      type: "object"
      required: []
      properties: {}
      description: ".DS_Store (Model)"
    Company:
      type: "object"
      required: []
      properties:
        id:
          type: "number"
          example: 173
        user_id:
          type: "number"
          example: 253
        client_id:
          type: "string"
          example: "Lorem Ipsum"
        client_secret:
          type: "string"
          example: "Lorem Ipsum"
        scope:
          type: "string"
          example: "Lorem Ipsum"
        access_token:
          type: "string"
          example: "Lorem Ipsum"
        token_expires_in:
          type: "number"
          example: 280
        token_expires_at:
          type: "string"
          example: "2021-03-23T16:13:08.489+01:00"
          format: "date-time"
        name:
          $ref: "#/components/schemas/string // could be company name or individual name"
          example: "John Doe"
        tin_code:
          $ref: "#/components/schemas/string // e.g C25845632020 (in case of taxpayer identified by TIN only)"
          example: null
        registration_number:
          $ref: "#/components/schemas/string // eg.201901234567, when combine with tin code will be IG12345678912"
          example: null
        registration_type:
          $ref: "#/components/schemas/RegistrationType"
          example: null
        sst_registration_number:
          type: "string"
          example: "Lorem Ipsum"
        tourism_tax_registration_number:
          type: "string"
          example: "Lorem Ipsum"
        business_activity_description:
          type: "string"
          example: "Lorem Ipsum"
        msic_code:
          type: "string"
          example: "Lorem Ipsum"
        country:
          type: "string"
          example: "United States of America"
        state:
          type: "string"
          example: "Lorem Ipsum"
        zip_code:
          type: "string"
          example: "Lorem Ipsum"
        city:
          type: "string"
          example: "Chicago"
        address:
          type: "string"
          example: "1028 Farland Street"
        phone:
          type: "string"
          example: "Lorem Ipsum"
        email:
          type: "string"
          example: "<EMAIL>"
          format: "email"
        bank_account:
          type: "string"
          example: "Lorem Ipsum"
        exporter_certified_number:
          $ref: "#/components/schemas/string // only for exporting companies"
          example: null
        applicable_tax_types:
          type: "array"
          items:
            $ref: "#/components/schemas/string[] // Array of tax type codes"
            example: null
        sales_tax_rates:
          type: "array"
          items:
            $ref: "#/components/schemas/string[] // Array of sales tax rates (e.g., ['5%', '10%'])"
            example: null
        service_tax_rates:
          type: "array"
          items:
            $ref: "#/components/schemas/string[] // Array of service tax rates (e.g., ['6%', '8%'])"
            example: null
        setting:
          $ref: "#/components/schemas/CompanyAppFeatureSetting"
          example: null
        created_at:
          type: "string"
          example: "2021-03-23T16:13:08.489+01:00"
          format: "date-time"
        updated_at:
          type: "string"
          example: "2021-03-23T16:13:08.489+01:00"
          format: "date-time"
        user:
          $ref: "#/components/schemas/User"
          example: null
        orders:
          type: "array"
          items:
            $ref: "#/components/schemas/Order"
            example: null
        submitted_documents:
          type: "array"
          items:
            $ref: "#/components/schemas/SubmittedDocument"
            example: null
        is_company_ready:
          type: "boolean"
          example: true
      description: "Company (Model)"
    document_submission:
      type: "object"
      required: []
      properties:
        id:
          type: "number"
          example: 940
        user_id:
          type: "number"
          example: 866
        company_id:
          type: "number"
          example: 590
        submission_uid:
          type: "string"
          example: "Lorem Ipsum"
        total_documents:
          type: "number"
          example: 919
        created_at:
          type: "string"
          example: "2021-03-23T16:13:08.489+01:00"
          format: "date-time"
        updated_at:
          type: "string"
          example: "2021-03-23T16:13:08.489+01:00"
          format: "date-time"
        user:
          $ref: "#/components/schemas/User> // submitted by which use"
          example: null
        company:
          $ref: "#/components/schemas/Company> // this submitted docs belong to which compan"
          example: null
        submitted_documents:
          type: "array"
          items:
            $ref: "#/components/schemas/SubmittedDocument"
            example: null
      description: "document_submission (Model)"
    EinvoiceRequest:
      type: "object"
      required: []
      properties:
        id:
          type: "number"
          example: 107
        company_id:
          type: "number"
          example: 262
        invoice_code:
          type: "string"
          example: "Lorem Ipsum"
        document_details:
          $ref: "#/components/schemas/PartialEinvoiceV1"
          example: null
        status:
          $ref: "#/components/schemas/'Pending'"
          example: null
        created_at:
          type: "string"
          example: "2021-03-23T16:13:08.489+01:00"
          format: "date-time"
        updated_at:
          type: "string"
          example: "2021-03-23T16:13:08.489+01:00"
          format: "date-time"
        company:
          $ref: "#/components/schemas/Company"
          example: null
      description: "EinvoiceRequest (Model)"
    Order:
      type: "object"
      required: []
      properties:
        id:
          type: "number"
          example: 551
        company_id:
          $ref: "#/components/schemas/number // belongs to which company"
          example: null
        user_id:
          $ref: "#/components/schemas/number // belongs to which user (to search in db easier, rather than using companyId and check if companyId belongs to user.)"
          example: null
        is_submitted_to_lhdn:
          $ref: "#/components/schemas/boolean // status valid and submitted cannot change.."
          example: null
        invoice_code:
          $ref: "#/components/schemas/string // INVOICE CODE CANNOT BE REPEATED"
          example: null
        buyer:
          $ref: "#/components/schemas/Buyer"
          example: null
        supplier:
          $ref: "#/components/schemas/Supplier"
          example: null
        delivery_details:
          $ref: "#/components/schemas/DeliveryDetails"
          example: null
        invoice_date_time:
          $ref: "#/components/schemas/InvoiceDateTime"
          example: null
        foreign_currency:
          $ref: "#/components/schemas/ForeignCurrency"
          example: null
        billing_period:
          $ref: "#/components/schemas/BillingPeriod"
          example: null
        line_items:
          type: "array"
          items:
            $ref: "#/components/schemas/LineItem"
            example: null
        payment:
          $ref: "#/components/schemas/Payment"
          example: null
        pre_payment:
          $ref: "#/components/schemas/Prepayment"
          example: null
        billing_reference_number:
          type: "string"
          example: "Lorem Ipsum"
        legal_monetary_total:
          $ref: "#/components/schemas/LegalMonetaryTotal"
          example: null
        invoice_level_line_item_taxes:
          $ref: "#/components/schemas/InvoiceLevelLineItemTaxes"
          example: null
        invoice_level_allowance_charge:
          $ref: "#/components/schemas/InvoiceLevelAllowanceCharge"
          example: null
        additional_document_reference:
          type: "array"
          items:
            $ref: "#/components/schemas/AdditionalDocumentReference"
            example: null
        is_consolidate:
          type: "boolean"
          example: true
        is_ready:
          $ref: "#/components/schemas/boolean // is this order ready for submission"
          example: null
        external_id:
          type: "string"
          example: "Lorem Ipsum"
        created_at:
          type: "string"
          example: "2021-03-23T16:13:08.489+01:00"
          format: "date-time"
        updated_at:
          type: "string"
          example: "2021-03-23T16:13:08.489+01:00"
          format: "date-time"
        status:
          $ref: "#/components/schemas/"
          example: null
        company:
          $ref: "#/components/schemas/Company"
          example: null
        user:
          $ref: "#/components/schemas/User"
          example: null
        submitted_documents:
          type: "array"
          items:
            $ref: "#/components/schemas/SubmittedDocument"
            example: null
      description: "Order (Model)"
    ShopifyStore:
      type: "object"
      required: []
      properties:
        id:
          type: "number"
          example: 23
        shop_domain:
          type: "string"
          example: "Lorem Ipsum"
        shop_name:
          type: "string"
          example: "Lorem Ipsum"
        access_token:
          type: "string"
          example: "Lorem Ipsum"
        webhook_topic:
          type: "string"
          example: "Lorem Ipsum"
        webhook_address:
          type: "string"
          example: "Lorem Ipsum"
        user_id:
          type: "number"
          example: 327
        user:
          $ref: "#/components/schemas/User"
          example: null
        created_at:
          type: "string"
          example: "2021-03-23T16:13:08.489+01:00"
          format: "date-time"
        updated_at:
          type: "string"
          example: "2021-03-23T16:13:08.489+01:00"
          format: "date-time"
      description: "ShopifyStore (Model)"
    SubmittedDocument:
      type: "object"
      required: []
      properties:
        id:
          type: "number"
          example: 153
        document_submission_id:
          type: "number"
          example: 147
        order_id:
          type: "number"
          example: 397
        company_id:
          type: "number"
          example: 670
        user_id:
          $ref: "#/components/schemas/number // easier to preload and search from db (instead of preload company and then use company to find the user)"
          example: null
        code:
          $ref: "#/components/schemas/string // Code for document could be invoice code, credit note code, debit note code etc."
          example: null
        uuid:
          type: "string"
          example: "Lorem Ipsum"
        status:
          $ref: "#/components/schemas/'Submitted'"
          example: null
        fail_reason:
          type: "string"
          example: "Lorem Ipsum"
        fail_details:
          type: "array"
          items:
            $ref: "#/components/schemas/Any"
            example: null
        document_details:
          $ref: "#/components/schemas/Object // This is UBL format snapshot"
          example: null
        type:
          $ref: "#/components/schemas/'Invoice' //"
          example: null
        cancel_reason:
          type: "string"
          example: "Lorem Ipsum"
        long_id:
          type: "string"
          example: "Lorem Ipsum"
        created_at:
          type: "string"
          example: "2021-03-23T16:13:08.489+01:00"
          format: "date-time"
        updated_at:
          type: "string"
          example: "2021-03-23T16:13:08.489+01:00"
          format: "date-time"
        submission:
          $ref: "#/components/schemas/DocumentSubmission"
          example: null
        order:
          $ref: "#/components/schemas/Order"
          example: null
        company:
          $ref: "#/components/schemas/Company"
          example: null
        user:
          $ref: "#/components/schemas/User"
          example: null
      description: "SubmittedDocument (Model)"
    User:
      type: "object"
      required: []
      properties:
        id:
          type: "number"
          example: 916
        full_name:
          type: "string"
          example: "John Doe"
        email:
          type: "string"
          example: "<EMAIL>"
          format: "email"
        api_key:
          type: "string"
          example: "Lorem Ipsum"
        auth_type:
          $ref: "#/components/schemas/'password'"
          example: null
        created_at:
          type: "string"
          example: "2021-03-23T16:13:08.489+01:00"
          format: "date-time"
        updated_at:
          type: "string"
          example: "2021-03-23T16:13:08.489+01:00"
          format: "date-time"
        external_id:
          type: "string"
          example: "Lorem Ipsum"
        document_submissions:
          type: "array"
          items:
            $ref: "#/components/schemas/DocumentSubmission"
            example: null
        companies:
          type: "array"
          items:
            $ref: "#/components/schemas/Company"
            example: null
        shopify_stores:
          type: "array"
          items:
            $ref: "#/components/schemas/ShopifyStore"
            example: null
        orders:
          type: "array"
          items:
            $ref: "#/components/schemas/Order"
            example: null
        submitted_documents:
          type: "array"
          items:
            $ref: "#/components/schemas/SubmittedDocument"
            example: null
      description: "User (Model)"
    PaginationMeta:
      type: "object"
      properties:
        total:
          type: "number"
          example: 100
          nullable: false
        page:
          type: "number"
          example: 2
          nullable: false
        perPage:
          type: "number"
          example: 10
          nullable: false
        currentPage:
          type: "number"
          example: 3
          nullable: false
        lastPage:
          type: "number"
          example: 10
          nullable: false
        firstPage:
          type: "number"
          example: 1
          nullable: false
        lastPageUrl:
          type: "string"
          example: "/?page=10"
          nullable: false
        firstPageUrl:
          type: "string"
          example: "/?page=1"
          nullable: false
        nextPageUrl:
          type: "string"
          example: "/?page=6"
          nullable: false
        previousPageUrl:
          type: "string"
          example: "/?page=5"
          nullable: false
paths:
  /api/v1/orders:
    get:
      summary: "List orders (index)"
      description: "Returns **200** (OK) as **application/json**\n\n _app/controllers/http/orders_controller.ts_ - **index**"
      operationId: "index"
      parameters:
        - in: "eter"
          name: "page query false"
          description: "Page number for pagination (default: 1)"
          schema:
            example: null
            type: "string"
          required: true
        - in: "eter"
          name: "per_page query false"
          description: "Number of items per page (default: 10)"
          schema:
            example: null
            type: "string"
          required: true
        - in: "eter"
          name: "sort query false"
          description: "Sort field and direction (default: created_at:desc)"
          schema:
            example: null
            type: "string"
          required: true
      tags:
        - "ORDERS"
      responses:
        200:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  data: "array"
                  meta:
                    type: "object"
                    properties:
                      total: "number"
                      per_page: "number"
                      current_page: "number"
                      last_page: "number"
              example:
                data: "array"
                meta:
                  total: "number"
                  per_page: "number"
                  current_page: "number"
                  last_page: "number"
          description: "Returns **200** (OK) as **application/json**"
        401:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Unauthorized"
              example:
                error: "Unauthorized"
          description: "Returns **401** (Unauthorized) as **application/json**"
        404:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  message: "Company not found"
              example:
                message: "Company not found"
          description: "Returns **404** (Not Found) as **application/json**"
        500:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Internal server error"
              example:
                error: "Internal server error"
          description: "Returns **500** (Internal Server Error) as **application/json**"
      security: []
    post:
      summary: "Create a new order (store)"
      description: "Create a new order/invoice draft that can be submitted to MyInvois later\n\n _app/controllers/http/orders_controller.ts_ - **store**"
      operationId: "store"
      parameters: []
      tags:
        - "ORDERS"
      responses:
        200:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  success: "boolean"
                  data:
                    type: "object"
                    properties:
                      id: "number"
                      invoice_code: "string"
                      status: "string"
                      created_at: "string"
                      updated_at: "string"
              example:
                success: "boolean"
                data:
                  id: "number"
                  invoice_code: "string"
                  status: "string"
                  created_at: "string"
                  updated_at: "string"
          description: "Returns **200** (OK) as **application/json**"
        400:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Bad Request"
              example:
                error: "Bad Request"
          description: "Returns **400** (Bad Request) as **application/json**"
        401:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Unauthorized"
              example:
                error: "Unauthorized"
          description: "Returns **401** (Unauthorized) as **application/json**"
        409:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  success: "boolean"
                  message: "Invoice code already exists"
              example:
                success: "boolean"
                message: "Invoice code already exists"
          description: "Returns **409** (Conflict) as **application/json**"
        422:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "validation_error"
                  messages: "array"
              example:
                error: "validation_error"
                messages: "array"
          description: "Returns **422** (Unprocessable Entity) as **application/json**"
        500:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Internal server error"
              example:
                error: "Internal server error"
          description: "Returns **500** (Internal Server Error) as **application/json**"
      security: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/manualInvoiceSingleSchema"
            example: {}
  /api/v1/orders/create:
    get:
      summary: " (create)"
      description: "\n\n _app/controllers/http/orders_controller.ts_ - **create**"
      parameters: []
      tags:
        - "ORDERS"
      responses:
        200:
          description: "OK"
          content:
            application/json: {}
      security: []
  /api/v1/orders/{id}:
    get:
      summary: "Get a specific order (show)"
      description: "Returns **200** (OK) as **application/json**\n\n _app/controllers/http/orders_controller.ts_ - **show**"
      operationId: "show"
      parameters:
        - in: "path"
          name: "id"
          schema:
            type: "string"
          required: true
        - in: "eter"
          name: "id path true"
          description: "Order ID to retrieve"
          schema:
            example: null
            type: "string"
          required: true
      tags:
        - "ORDERS"
      responses:
        200:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  data:
                    type: "object"
                    properties:
                      id: "number"
                      invoice_code: "string"
                      status: "string"
                      buyer: "object"
                      supplier: "object"
                      line_items: "array"
                      submitted_documents: "array"
                      created_at: "string"
                      updated_at: "string"
              example:
                data:
                  id: "number"
                  invoice_code: "string"
                  status: "string"
                  buyer: "object"
                  supplier: "object"
                  line_items: "array"
                  submitted_documents: "array"
                  created_at: "string"
                  updated_at: "string"
          description: "Returns **200** (OK) as **application/json**"
        401:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Unauthorized"
              example:
                error: "Unauthorized"
          description: "Returns **401** (Unauthorized) as **application/json**"
        404:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  message: "Company not found"
              example:
                message: "Company not found"
          description: "Returns **404** (Not Found) as **application/json**"
        500:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Internal server error"
              example:
                error: "Internal server error"
          description: "Returns **500** (Internal Server Error) as **application/json**"
      security: []
    put:
      summary: "Update an order (update)"
      description: "Update an existing order (only allowed for draft orders that haven't been submitted)\n\n _app/controllers/http/orders_controller.ts_ - **update**"
      operationId: "update"
      parameters:
        - in: "path"
          name: "id"
          schema:
            type: "string"
          required: true
        - in: "eter"
          name: "id path true"
          description: "Order ID to update"
          schema:
            example: null
            type: "string"
          required: true
      tags:
        - "ORDERS"
      responses:
        200:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  success: "boolean"
                  data:
                    type: "object"
                    properties:
                      id: "number"
                      invoice_code: "string"
                      status: "string"
                      updated_at: "string"
              example:
                success: "boolean"
                data:
                  id: "number"
                  invoice_code: "string"
                  status: "string"
                  updated_at: "string"
          description: "Returns **200** (OK) as **application/json**"
        400:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Bad Request"
              example:
                error: "Bad Request"
          description: "Returns **400** (Bad Request) as **application/json**"
        401:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Unauthorized"
              example:
                error: "Unauthorized"
          description: "Returns **401** (Unauthorized) as **application/json**"
        403:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  success: "boolean"
                  message: "Order cannot be updated"
              example:
                success: "boolean"
                message: "Order cannot be updated"
          description: "Returns **403** (Forbidden) as **application/json**"
        404:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Order not found"
              example:
                error: "Order not found"
          description: "Returns **404** (Not Found) as **application/json**"
        409:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  success: "boolean"
                  message: "Invoice code already exists"
              example:
                success: "boolean"
                message: "Invoice code already exists"
          description: "Returns **409** (Conflict) as **application/json**"
        422:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "validation_error"
                  messages: "array"
              example:
                error: "validation_error"
                messages: "array"
          description: "Returns **422** (Unprocessable Entity) as **application/json**"
        500:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Internal server error"
              example:
                error: "Internal server error"
          description: "Returns **500** (Internal Server Error) as **application/json**"
      security: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateOrderSchema"
            example: {}
    delete:
      summary: "Delete an order (destroy)"
      description: "Delete an existing order (only allowed for draft orders that haven't been submitted)\n\n _app/controllers/http/orders_controller.ts_ - **destroy**"
      operationId: "destroy"
      parameters:
        - in: "path"
          name: "id"
          schema:
            type: "string"
          required: true
        - in: "eter"
          name: "id path true"
          description: "Order ID to delete"
          schema:
            example: null
            type: "string"
          required: true
      tags:
        - "ORDERS"
      responses:
        200:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  message: "Order deleted successfully"
              example:
                message: "Order deleted successfully"
          description: "Returns **200** (OK) as **application/json**"
        401:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Unauthorized"
              example:
                error: "Unauthorized"
          description: "Returns **401** (Unauthorized) as **application/json**"
        403:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  success: "boolean"
                  message: "Order cannot be deleted"
              example:
                success: "boolean"
                message: "Order cannot be deleted"
          description: "Returns **403** (Forbidden) as **application/json**"
        404:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Order not found"
              example:
                error: "Order not found"
          description: "Returns **404** (Not Found) as **application/json**"
        500:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Internal server error"
              example:
                error: "Internal server error"
          description: "Returns **500** (Internal Server Error) as **application/json**"
      security: []
  /api/v1/orders/{id}/edit:
    get:
      summary: " (edit)"
      description: "\n\n _app/controllers/http/orders_controller.ts_ - **edit**"
      parameters:
        - in: "path"
          name: "id"
          schema:
            type: "string"
          required: true
      tags:
        - "ORDERS"
      responses:
        200:
          description: "OK"
          content:
            application/json: {}
      security: []
  /api/v1/orders/{id}/submit:
    post:
      summary: "Submit order to MyInvois (submitOrderToMyInvois)"
      description: "Submit a draft order to MyInvois for validation and processing\n\n _app/controllers/http/orders_controller.ts_ - **submitOrderToMyInvois**"
      operationId: "submitOrderToMyInvois"
      parameters:
        - in: "path"
          name: "id"
          schema:
            type: "string"
          required: true
        - in: "eter"
          name: "id path true"
          description: "Order ID to submit"
          schema:
            example: null
            type: "string"
          required: true
      tags:
        - "ORDERS"
      responses:
        200:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  success: "boolean"
                  data:
                    type: "object"
                    properties:
                      id: "number"
                      status: "string"
                      submitted_document: "object"
              example:
                success: "boolean"
                data:
                  id: "number"
                  status: "string"
                  submitted_document: "object"
          description: "Returns **200** (OK) as **application/json**"
        400:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Bad Request"
              example:
                error: "Bad Request"
          description: "Returns **400** (Bad Request) as **application/json**"
        401:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Unauthorized"
              example:
                error: "Unauthorized"
          description: "Returns **401** (Unauthorized) as **application/json**"
        403:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  success: "boolean"
                  message: "Order cannot be submitted"
              example:
                success: "boolean"
                message: "Order cannot be submitted"
          description: "Returns **403** (Forbidden) as **application/json**"
        404:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Order not found"
              example:
                error: "Order not found"
          description: "Returns **404** (Not Found) as **application/json**"
        500:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Internal server error"
              example:
                error: "Internal server error"
          description: "Returns **500** (Internal Server Error) as **application/json**"
      security: []
  /api/v1/orders/validate:
    post:
      summary: "Validate invoice code uniqueness (validateInvoiceCodes)"
      description: "Check if an invoice code is unique within the company's orders\n\n _app/controllers/http/orders_controller.ts_ - **validateInvoiceCodes**"
      operationId: "validateInvoiceCodes"
      parameters:
        - in: "eter"
          name: "invoice_code query true"
          description: "Invoice code to validate"
          schema:
            example: null
            type: "string"
          required: true
      tags:
        - "ORDERS"
      responses:
        200:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  data:
                    type: "object"
                    properties:
                      success: "boolean"
              example:
                data:
                  success: "boolean"
          description: "Returns **200** (OK) as **application/json**"
        400:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Bad Request"
              example:
                error: "Bad Request"
          description: "Returns **400** (Bad Request) as **application/json**"
        401:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Unauthorized"
              example:
                error: "Unauthorized"
          description: "Returns **401** (Unauthorized) as **application/json**"
        404:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  message: "Company not found"
              example:
                message: "Company not found"
          description: "Returns **404** (Not Found) as **application/json**"
        500:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Internal server error"
              example:
                error: "Internal server error"
          description: "Returns **500** (Internal Server Error) as **application/json**"
      security: []
  /api/v1/companies:
    get:
      summary: " (showMyCompany)"
      description: "\n\n _app/controllers/http/companies_controller.ts_ - **showMyCompany**"
      parameters: []
      tags:
        - "COMPANIES"
      responses:
        200:
          description: "OK"
          content:
            application/json: {}
      security: []
    put:
      summary: " (updateMyCompany)"
      description: "\n\n _app/controllers/http/companies_controller.ts_ - **updateMyCompany**"
      parameters: []
      tags:
        - "COMPANIES"
      responses:
        204:
          description: "No Content"
          content:
            application/json: {}
      security: []
      requestBody:
        content:
          application/json: {}
  /api/v1/tin-validation:
    post:
      summary: "Validate TIN (validateTin)"
      description: "Validate TIN\n\n _app/controllers/http/companies_controller.ts_ - **validateTin**"
      parameters: []
      tags:
        - "COMPANIES"
      responses:
        200:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  data:
                    type: "object"
                    properties:
                      valid: "boolean"
              example:
                data:
                  valid: "boolean"
          description: "Returns **200** (OK) as **application/json**"
        400:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "string"
                  error_description: "string"
                  error_uri: "string"
              example:
                error: "string"
                error_description: "string"
                error_uri: "string"
          description: "Returns **400** (Bad Request) as **application/json**"
        500:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Internal server error"
              example:
                error: "Internal server error"
          description: "Returns **500** (Internal Server Error) as **application/json**"
      security: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/tinValidationSchema"
            example: {}
  /api/v1/submit-consolidated-invoices:
    post:
      summary: "Submit consolidate orders to my invois API (submitConsolidateOrdersToMyInvoice)"
      description: "Submit consolidate orders to my invois API\n\n _app/controllers/http/orders_controller.ts_ - **submitConsolidateOrdersToMyInvoice**"
      operationId: "submitConsolidateOrdersToMyInvoice"
      parameters: []
      tags:
        - "ORDERS"
      responses:
        200:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  data:
                    type: "object"
                    properties:
                      success: "boolean"
                      message: "string"
              example:
                data:
                  success: "boolean"
                  message: "string"
          description: "Returns **200** (OK) as **application/json**"
        400:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Bad Request"
              example:
                error: "Bad Request"
          description: "Returns **400** (Bad Request) as **application/json**"
        401:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Unauthorized"
              example:
                error: "Unauthorized"
          description: "Returns **401** (Unauthorized) as **application/json**"
        500:
          content:
            application/json:
              schema:
                type: "object"
                properties:
                  error: "Internal server error"
              example:
                error: "Internal server error"
          description: "Returns **500** (Internal Server Error) as **application/json**"
      security: []
  /scalar:
    get:
      summary: "route"
      description: "\n\n __ - ****"
      parameters: []
      tags: []
      responses:
        200:
          description: "OK"
          content:
            application/json: {}
      security: []
tags:
  - name: "ORDERS"
    description: "Everything related to ORDERS"
  - name: "COMPANIES"
    description: "Everything related to COMPANIES"