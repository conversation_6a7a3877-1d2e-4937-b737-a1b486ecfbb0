---
title: Code Examples
description: Practical code examples and usage scenarios for common API operations
---

# Code Examples

This section provides practical code examples in multiple programming languages for common BizCare MyInvois API operations. Use these examples as a starting point for your integration.

## Quick Start Examples

### JavaScript/Node.js

#### Basic Setup

```javascript
const API_BASE_URL = 'https://api.myinvois.bizcare.my/api';
const API_KEY = 'your-api-key-here';

const headers = {
  'X-API-Key': API_KEY,
  'Content-Type': 'application/json'
};

async function apiRequest(endpoint, options = {}) {
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    headers,
    ...options
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(`API Error: ${error.message}`);
  }
  
  return response.json();
}
```

#### Create an Order

```javascript
async function createOrder() {
  const orderData = {
    invoice_code: `INV-${Date.now()}`,
    buyer: {
      name: "Customer Company Sdn Bhd",
      tin: "C12345678901",
      address: "123 Customer Street",
      city: "Kuala Lumpur",
      state: "Selangor",
      zip_code: "50000",
      country: "Malaysia"
    },
    line_items: [
      {
        description: "Professional Consulting Services",
        quantity: 10,
        unit_price: 150.00,
        unit_code: "HUR",
        classification_code: "003",
        tax_type: "02",
        tax_rate: 8.00,
        tax_amount: 120.00,
        total_amount: 1620.00
      }
    ],
    invoice_date_time: new Date().toISOString()
  };

  try {
    const result = await apiRequest('/orders', {
      method: 'POST',
      body: JSON.stringify(orderData)
    });
    
    console.log('Order created:', result.data);
    return result.data;
  } catch (error) {
    console.error('Failed to create order:', error.message);
    throw error;
  }
}
```

#### Submit Order to MyInvois

```javascript
async function submitOrder(orderId) {
  try {
    const result = await apiRequest(`/orders/${orderId}/submit`, {
      method: 'POST',
      body: JSON.stringify({})
    });
    
    console.log('Order submitted:', result.data);
    return result.data;
  } catch (error) {
    console.error('Failed to submit order:', error.message);
    throw error;
  }
}
```

---

### Python

#### Basic Setup

```python
import requests
import json
from datetime import datetime

API_BASE_URL = 'https://api.myinvois.bizcare.my/api'
API_KEY = 'your-api-key-here'

class EInvoiceAPI:
    def __init__(self, api_key, base_url=API_BASE_URL):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            'X-API-Key': api_key,
            'Content-Type': 'application/json'
        }
    
    def _request(self, method, endpoint, data=None):
        url = f"{self.base_url}{endpoint}"
        
        response = requests.request(
            method=method,
            url=url,
            headers=self.headers,
            json=data if data else None
        )
        
        if not response.ok:
            error_data = response.json()
            raise Exception(f"API Error: {error_data.get('message', 'Unknown error')}")
        
        return response.json()
    
    def create_order(self, order_data):
        return self._request('POST', '/orders', order_data)
    
    def get_order(self, order_id):
        return self._request('GET', f'/orders/{order_id}')
    
    def submit_order(self, order_id):
        return self._request('POST', f'/orders/{order_id}/submit', {})
    
    def list_orders(self, page=1, per_page=10):
        return self._request('GET', f'/orders?page={page}&per_page={per_page}')
```

#### Create and Submit Order

```python
def create_and_submit_order():
    api = EInvoiceAPI(API_KEY)
    
    # Create order
    order_data = {
        'invoice_code': f'INV-{int(datetime.now().timestamp())}',
        'buyer': {
            'name': 'Customer Company Sdn Bhd',
            'tin': 'C12345678901',
            'address': '123 Customer Street',
            'city': 'Kuala Lumpur',
            'state': 'Selangor',
            'zip_code': '50000',
            'country': 'Malaysia'
        },
        'line_items': [
            {
                'description': 'Professional Consulting Services',
                'quantity': 10,
                'unit_price': 150.00,
                'unit_code': 'HUR',
                'classification_code': '003',
                'tax_type': '02',
                'tax_rate': 8.00,
                'tax_amount': 120.00,
                'total_amount': 1620.00
            }
        ],
        'invoice_date_time': datetime.now().isoformat()
    }
    
    try:
        # Create the order
        create_result = api.create_order(order_data)
        order_id = create_result['data']['id']
        print(f"Order created with ID: {order_id}")
        
        # Submit to MyInvois
        submit_result = api.submit_order(order_id)
        print(f"Order submitted: {submit_result['data']}")
        
        return order_id
        
    except Exception as e:
        print(f"Error: {e}")
        raise
```

---

### PHP

#### Basic Setup

```php
<?php

class EInvoiceAPI {
    private $apiKey;
    private $baseUrl;
    
    public function __construct($apiKey, $baseUrl = 'https://api.myinvois.bizcare.my/api') {
        $this->apiKey = $apiKey;
        $this->baseUrl = $baseUrl;
    }
    
    private function request($method, $endpoint, $data = null) {
        $url = $this->baseUrl . $endpoint;
        
        $headers = [
            'X-API-Key: ' . $this->apiKey,
            'Content-Type: application/json'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        $decodedResponse = json_decode($response, true);
        
        if ($httpCode >= 400) {
            throw new Exception('API Error: ' . ($decodedResponse['message'] ?? 'Unknown error'));
        }
        
        return $decodedResponse;
    }
    
    public function createOrder($orderData) {
        return $this->request('POST', '/orders', $orderData);
    }
    
    public function getOrder($orderId) {
        return $this->request('GET', "/orders/{$orderId}");
    }
    
    public function submitOrder($orderId) {
        return $this->request('POST', "/orders/{$orderId}/submit", []);
    }
    
    public function listOrders($page = 1, $perPage = 10) {
        return $this->request('GET', "/orders?page={$page}&per_page={$perPage}");
    }
}

// Usage example
$api = new EInvoiceAPI('your-api-key-here');

$orderData = [
    'invoice_code' => 'INV-' . time(),
    'buyer' => [
        'name' => 'Customer Company Sdn Bhd',
        'tin' => 'C12345678901',
        'address' => '123 Customer Street',
        'city' => 'Kuala Lumpur',
        'state' => 'Selangor',
        'zip_code' => '50000',
        'country' => 'Malaysia'
    ],
    'line_items' => [
        [
            'description' => 'Professional Consulting Services',
            'quantity' => 10,
            'unit_price' => 150.00,
            'unit_code' => 'HUR',
            'classification_code' => '003',
            'tax_type' => '02',
            'tax_rate' => 8.00,
            'tax_amount' => 120.00,
            'total_amount' => 1620.00
        ]
    ],
    'invoice_date_time' => date('c')
];

try {
    $result = $api->createOrder($orderData);
    echo "Order created: " . json_encode($result['data']) . "\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
```

---

## Common Use Cases

### 1. Bulk Order Creation

```javascript
async function createBulkOrders(ordersData) {
  const results = [];
  
  for (const orderData of ordersData) {
    try {
      // Add unique invoice code
      orderData.invoice_code = `INV-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      const result = await apiRequest('/orders', {
        method: 'POST',
        body: JSON.stringify(orderData)
      });
      
      results.push({ success: true, data: result.data });
      
      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 100));
      
    } catch (error) {
      results.push({ success: false, error: error.message, orderData });
    }
  }
  
  return results;
}
```

### 2. Order Status Monitoring

```javascript
async function monitorOrderStatus(orderId, maxAttempts = 10) {
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      const order = await apiRequest(`/orders/${orderId}`);
      const status = order.data.status;
      
      console.log(`Attempt ${attempt}: Order status is ${status}`);
      
      if (status === 'Valid' || status === 'Invalid') {
        return order.data;
      }
      
      // Wait before next check
      await new Promise(resolve => setTimeout(resolve, 5000));
      
    } catch (error) {
      console.error(`Error checking status: ${error.message}`);
    }
  }
  
  throw new Error('Order status check timeout');
}
```

### 3. Invoice Code Validation

```javascript
async function validateAndCreateOrder(orderData) {
  try {
    // First, validate the invoice code
    const validation = await apiRequest(`/orders/validate?invoice_code=${orderData.invoice_code}`, {
      method: 'POST'
    });
    
    if (!validation.data.success) {
      throw new Error('Invoice code already exists');
    }
    
    // If valid, create the order
    const result = await apiRequest('/orders', {
      method: 'POST',
      body: JSON.stringify(orderData)
    });
    
    return result.data;
    
  } catch (error) {
    console.error('Validation or creation failed:', error.message);
    throw error;
  }
}
```

### 4. Company Information Update

```javascript
async function updateCompanyTaxSettings(taxSettings) {
  const updateData = {
    applicable_tax_types: taxSettings.taxTypes,
    sales_tax_rates: taxSettings.salesTaxRates,
    service_tax_rates: taxSettings.serviceTaxRates
  };

  try {
    const result = await apiRequest('/companies', {
      method: 'PUT',
      body: JSON.stringify(updateData)
    });

    console.log('Company tax settings updated successfully');
    return result;

  } catch (error) {
    console.error('Failed to update company settings:', error.message);
    throw error;
  }
}
```

### 5. Consolidated Invoice Submission

```javascript
async function submitConsolidatedInvoices(orderIds) {
  const submissionData = {
    order_ids: orderIds,
    submission_type: 'consolidated'
  };

  try {
    const result = await apiRequest('/submit-consolidated-invoices', {
      method: 'POST',
      body: JSON.stringify(submissionData)
    });

    console.log(`Successfully submitted ${orderIds.length} orders in consolidated batch`);
    return result.data;

  } catch (error) {
    console.error('Consolidated submission failed:', error.message);
    throw error;
  }
}
```

---

## Integration Patterns

### 1. Complete Order Workflow

```javascript
class OrderWorkflow {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.headers = {
      'X-API-Key': apiKey,
      'Content-Type': 'application/json'
    };
  }

  async processOrder(orderData) {
    try {
      // Step 1: Validate invoice code
      await this.validateInvoiceCode(orderData.invoice_code);

      // Step 2: Create order
      const order = await this.createOrder(orderData);

      // Step 3: Submit to MyInvois
      const submission = await this.submitOrder(order.id);

      // Step 4: Monitor status
      const finalStatus = await this.monitorSubmission(order.id);

      return {
        order,
        submission,
        finalStatus
      };

    } catch (error) {
      console.error('Order workflow failed:', error.message);
      throw error;
    }
  }

  async validateInvoiceCode(invoiceCode) {
    const response = await fetch(`${API_BASE_URL}/orders/validate?invoice_code=${invoiceCode}`, {
      method: 'POST',
      headers: this.headers
    });

    const result = await response.json();
    if (!result.data.success) {
      throw new Error('Invoice code already exists');
    }

    return true;
  }

  async createOrder(orderData) {
    const response = await fetch(`${API_BASE_URL}/orders`, {
      method: 'POST',
      headers: this.headers,
      body: JSON.stringify(orderData)
    });

    const result = await response.json();
    return result.data;
  }

  async submitOrder(orderId) {
    const response = await fetch(`${API_BASE_URL}/orders/${orderId}/submit`, {
      method: 'POST',
      headers: this.headers,
      body: JSON.stringify({})
    });

    const result = await response.json();
    return result.data;
  }

  async monitorSubmission(orderId, maxAttempts = 20) {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      const response = await fetch(`${API_BASE_URL}/orders/${orderId}`, {
        headers: this.headers
      });

      const order = await response.json();
      const status = order.data.status;

      if (status === 'Valid' || status === 'Invalid') {
        return order.data;
      }

      await new Promise(resolve => setTimeout(resolve, 3000));
    }

    throw new Error('Submission monitoring timeout');
  }
}

// Usage
const workflow = new OrderWorkflow('your-api-key');

const orderData = {
  invoice_code: 'INV-2024-001',
  buyer: {
    name: 'Customer Company Sdn Bhd',
    tin: 'C12345678901'
  },
  line_items: [
    {
      description: 'Consulting Services',
      quantity: 1,
      unit_price: 1000.00,
      unit_code: 'C62',
      classification_code: '003',
      tax_type: '02',
      tax_rate: 8.00,
      tax_amount: 80.00,
      total_amount: 1080.00
    }
  ]
};

workflow.processOrder(orderData)
  .then(result => console.log('Order processed successfully:', result))
  .catch(error => console.error('Order processing failed:', error));
```

### 2. Error Handling with Retry Logic

```javascript
class RobustAPIClient {
  constructor(apiKey, maxRetries = 3) {
    this.apiKey = apiKey;
    this.maxRetries = maxRetries;
    this.baseUrl = API_BASE_URL;
  }

  async request(endpoint, options = {}, retryCount = 0) {
    const url = `${this.baseUrl}${endpoint}`;
    const requestOptions = {
      headers: {
        'X-API-Key': this.apiKey,
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    try {
      const response = await fetch(url, requestOptions);

      if (response.ok) {
        return await response.json();
      }

      // Handle specific error cases
      if (response.status === 429) {
        // Rate limited - wait and retry
        const retryAfter = response.headers.get('Retry-After') || 60;
        console.log(`Rate limited. Waiting ${retryAfter} seconds...`);
        await this.sleep(retryAfter * 1000);
        return this.request(endpoint, options, retryCount);
      }

      if (response.status >= 500 && retryCount < this.maxRetries) {
        // Server error - retry with exponential backoff
        const delay = Math.pow(2, retryCount) * 1000;
        console.log(`Server error. Retrying in ${delay}ms...`);
        await this.sleep(delay);
        return this.request(endpoint, options, retryCount + 1);
      }

      // Client error or max retries reached
      const error = await response.json();
      throw new Error(`API Error (${response.status}): ${error.message}`);

    } catch (error) {
      if (retryCount < this.maxRetries && this.isRetryableError(error)) {
        const delay = Math.pow(2, retryCount) * 1000;
        console.log(`Network error. Retrying in ${delay}ms...`);
        await this.sleep(delay);
        return this.request(endpoint, options, retryCount + 1);
      }

      throw error;
    }
  }

  isRetryableError(error) {
    return error.name === 'TypeError' || // Network errors
           error.message.includes('fetch');
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

---

## Testing Examples

### Unit Tests (Jest)

```javascript
// api.test.js
const { EInvoiceAPI } = require('./api');

// Mock fetch
global.fetch = jest.fn();

describe('EInvoiceAPI', () => {
  let api;

  beforeEach(() => {
    api = new EInvoiceAPI('test-api-key');
    fetch.mockClear();
  });

  test('should create order successfully', async () => {
    const mockResponse = {
      success: true,
      data: {
        id: 123,
        invoice_code: 'INV-TEST-001',
        status: 'Draft'
      }
    };

    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse
    });

    const orderData = {
      invoice_code: 'INV-TEST-001',
      buyer: { name: 'Test Customer' },
      line_items: [{ description: 'Test Item', quantity: 1, unit_price: 100 }]
    };

    const result = await api.createOrder(orderData);

    expect(fetch).toHaveBeenCalledWith(
      'https://api.myinvois.bizcare.my/api/orders',
      expect.objectContaining({
        method: 'POST',
        headers: expect.objectContaining({
          'X-API-Key': 'test-api-key'
        }),
        body: JSON.stringify(orderData)
      })
    );

    expect(result).toEqual(mockResponse);
  });

  test('should handle API errors', async () => {
    fetch.mockResolvedValueOnce({
      ok: false,
      status: 422,
      json: async () => ({
        error: 'validation_error',
        message: 'Invalid data'
      })
    });

    await expect(api.createOrder({})).rejects.toThrow('Invalid data');
  });
});
```

---

## Best Practices

### 1. Environment Configuration

```javascript
// config.js
const config = {
  development: {
    apiUrl: 'https://dev-api.example.com/api',
    apiKey: process.env.DEV_API_KEY
  },
  staging: {
    apiUrl: 'https://staging-api.example.com/api',
    apiKey: process.env.STAGING_API_KEY
  },
  production: {
    apiUrl: 'https://api.example.com/api',
    apiKey: process.env.PROD_API_KEY
  }
};

const env = process.env.NODE_ENV || 'development';
module.exports = config[env];
```

### 2. Logging and Monitoring

```javascript
class APILogger {
  static logRequest(method, url, data) {
    console.log(`[API Request] ${method} ${url}`, {
      timestamp: new Date().toISOString(),
      data: data ? JSON.stringify(data) : null
    });
  }

  static logResponse(method, url, status, data) {
    console.log(`[API Response] ${method} ${url} - ${status}`, {
      timestamp: new Date().toISOString(),
      data: data ? JSON.stringify(data) : null
    });
  }

  static logError(method, url, error) {
    console.error(`[API Error] ${method} ${url}`, {
      timestamp: new Date().toISOString(),
      error: error.message,
      stack: error.stack
    });
  }
}
```

### 3. Data Validation

```javascript
function validateOrderData(orderData) {
  const errors = [];

  if (!orderData.invoice_code) {
    errors.push('Invoice code is required');
  }

  if (!orderData.buyer || !orderData.buyer.name) {
    errors.push('Buyer name is required');
  }

  if (!orderData.line_items || orderData.line_items.length === 0) {
    errors.push('At least one line item is required');
  }

  orderData.line_items?.forEach((item, index) => {
    if (!item.description) {
      errors.push(`Line item ${index + 1}: Description is required`);
    }
    if (!item.quantity || item.quantity <= 0) {
      errors.push(`Line item ${index + 1}: Quantity must be greater than 0`);
    }
    if (!item.unit_price || item.unit_price <= 0) {
      errors.push(`Line item ${index + 1}: Unit price must be greater than 0`);
    }
  });

  if (errors.length > 0) {
    throw new Error(`Validation errors: ${errors.join(', ')}`);
  }

  return true;
}
```

<Callout type="info">
  **API Rate Limits**: Remember to implement proper rate limiting and retry logic in your production applications.
</Callout>

<Callout type="warning">
  **Security**: Never expose your API keys in client-side code. Always use environment variables or secure configuration management.
</Callout>

<Callout type="tip">
  **Testing**: Test your integration thoroughly in a development environment before deploying to production.
</Callout>
