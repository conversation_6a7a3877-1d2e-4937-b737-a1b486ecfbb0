// apps/app/app/(authenticated)/(with-organization)/orders/components/submit-button.tsx
'use client';

import type { Order } from '@/app/types';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@repo/design-system/components/ui/dialog';
import { Input } from '@repo/design-system/components/ui/input';
import { toast } from '@repo/design-system/components/ui/sonner';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@repo/design-system/components/ui/tooltip';
import { cn } from '@repo/design-system/lib/utils';
import { ArrowBigLeftDash } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useCancelSubmittedDocument } from '../../hooks/useCancelSubmittedDocument';

interface SubmitButtonProps {
  status: string;
  order: Order;
}

export function CancelSubmittedDocumentButton({
  status,
  order,
}: SubmitButtonProps) {
  const [open, setOpen] = useState(false);
  const [cancelReason, setCancelReason] = useState('');
  const [fieldErrorReason, setFieldErrorReason] = useState('');

  const router = useRouter();
  const { cancelSubmittedDocumentFromMyInvois, isLoading } =
    useCancelSubmittedDocument();

  // Handle form submission
  const handleSubmit = async () => {
    if (cancelReason == '' || cancelReason == null) {
      toast.error('Please enter a reason for cancellation.');
      setFieldErrorReason('*Please enter a reason for cancellation.');
      return;
    }

    try {
      await cancelSubmittedDocumentFromMyInvois(order.id, cancelReason);
      toast.success('Cancel request sent to MyInvois successfully');

      router.push('/orders');
    } catch (error) {
      toast.error('Failed to cancel. Please try again.');
    }
  };

  order.submitted_documents.sort(
    (a, b) =>
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  );
  const latestSubmittedDocument = order.submitted_documents[0];
  const inputDate = new Date(latestSubmittedDocument.created_at);
  const now = new Date();
  const timeDifference = now.getTime() - inputDate.getTime();
  const seventyTwoHoursInMilliseconds = 72 * 60 * 60 * 1000;

  const isWithin72Hours = timeDifference <= seventyTwoHoursInMilliseconds;

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div>
          <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
              <Button
                variant="destructive"
                size="lg"
                type="button"
                disabled={
                  isLoading ||
                  !['Submitted', 'Valid'].includes(status) ||
                  !isWithin72Hours
                }
              >
                <ArrowBigLeftDash />
                {isLoading ? 'Cancelling...' : 'Cancel'}
              </Button>
            </DialogTrigger>
            <DialogContent className="overflow-y-auto sm:max-h-[80vh] sm:max-w-[900px]">
              <DialogHeader>
                <DialogTitle>Reason*</DialogTitle>
              </DialogHeader>
              <div>
                <Input
                  placeholder="eg. Wrong invoice details"
                  value={cancelReason}
                  onChange={(e) => setCancelReason(e.target.value)}
                  required
                />
                <p className={cn('text-destructive text-sm')}>
                  {fieldErrorReason}
                </p>
              </div>

              <div className="flex justify-end">
                <Button
                  variant="destructive"
                  size="sm"
                  type="button"
                  disabled={
                    isLoading || !['Submitted', 'Valid'].includes(status)
                  }
                  onClick={handleSubmit}
                >
                  <ArrowBigLeftDash />
                  {isLoading ? 'Cancelling...' : 'Cancel'}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </TooltipTrigger>
      <TooltipContent>
        <p>
          Cancel Valid/Submitted status latest submitted document that was
          submitted last 72 hours
        </p>
      </TooltipContent>
    </Tooltip>
  );
}
