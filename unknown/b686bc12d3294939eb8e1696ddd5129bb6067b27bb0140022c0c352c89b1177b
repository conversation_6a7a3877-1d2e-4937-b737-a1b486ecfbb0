---
title: Orders API
description: Manage invoice orders and submissions to MyInvois
---

# Orders API

The Orders API allows you to create, manage, and submit invoice orders to the Malaysia LHDN MyInvois system. Orders represent invoice drafts that can be validated and submitted for e-invoice compliance.

## Base Endpoint

```
/api/orders
```

## Order Lifecycle

1. **Draft** - Order is created but not yet submitted
2. **Ready** - Order is validated and ready for submission
3. **Submitted** - Order has been sent to MyInvois
4. **Valid** - MyInvois has validated the order
5. **Invalid** - MyInvois has rejected the order

## Endpoints Overview

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/orders` | List all orders |
| POST | `/orders` | Create a new order |
| GET | `/orders/{id}` | Get a specific order |
| PUT | `/orders/{id}` | Update an order |
| DELETE | `/orders/{id}` | Delete an order |
| POST | `/orders/{id}/submit` | Submit order to MyInvois |
| POST | `/orders/validate` | Validate invoice code |
| POST | `/submit-consolidated-invoices` | Submit a consolidated batch |

---

## List Orders

Retrieve a paginated list of orders for your company.

```http
GET /api/orders
```

### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `page` | integer | No | Page number (default: 1) |
| `per_page` | integer | No | Items per page (default: 10) |
| `sort` | string | No | Sort field and direction (default: created_at:desc) |

### Example Request

```bash
curl -X GET "https://api.myinvois.bizcare.my/api/orders?page=1&per_page=20" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json"
```

### Example Response

```json
{
  "data": [
    {
      "id": 551,
      "invoice_code": "INV-2024-001",
      "status": "Draft",
      "is_ready": false,
      "is_submitted_to_lhdn": false,
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z",
      "buyer": {
        "name": "ABC Company Sdn Bhd",
        "tin": "C12345678901"
      },
      "supplier": {
        "name": "Your Company Sdn Bhd",
        "tin": "C98765432109"
      },
      "line_items": [
        {
          "description": "Professional Services",
          "quantity": 1,
          "unit_price": 1000.00,
          "total_amount": 1000.00
        }
      ]
    }
  ],
  "meta": {
    "total": 150,
    "per_page": 20,
    "current_page": 1,
    "last_page": 8
  }
}
```

### Response Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 401 | Unauthorized |
| 404 | Company not found |
| 500 | Internal server error |

---

## Create Order

Create a new invoice order draft.

```http
POST /api/orders
```

### Request Body

The request body should contain the complete order information including buyer, supplier, line items, and tax details.

### Example Request

```bash
curl -X POST "https://api.myinvois.bizcare.my/api/orders" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "invoice_code": "INV-2024-002",
    "buyer": {
      "name": "Customer Company Sdn Bhd",
      "tin": "C12345678901",
      "address": "123 Business Street",
      "city": "Kuala Lumpur",
      "state": "Selangor",
      "zip_code": "50000",
      "country": "Malaysia"
    },
    "line_items": [
      {
        "description": "Consulting Services",
        "quantity": 10,
        "unit_price": 150.00,
        "classification_code": "003",
        "unit_code": "HUR",
        "tax_type": "02",
        "tax_rate": 8.00,
        "tax_amount": 120.00,
        "total_amount": 1620.00
      }
    ],
    "invoice_date_time": "2024-01-15T10:30:00Z"
  }'
```

### Example Response

```json
{
  "success": true,
  "data": {
    "id": 552,
    "invoice_code": "INV-2024-002",
    "status": "Draft",
    "created_at": "2024-01-15T11:00:00Z",
    "updated_at": "2024-01-15T11:00:00Z"
  }
}
```

### Response Codes

| Code | Description |
|------|-------------|
| 200 | Order created successfully |
| 400 | Bad request |
| 401 | Unauthorized |
| 409 | Invoice code already exists |
| 422 | Validation error |
| 500 | Internal server error |

---

## Get Order

Retrieve details of a specific order.

```http
GET /api/orders/{id}
```

### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | integer | Yes | Order ID |

### Example Request

```bash
curl -X GET "https://api.myinvois.bizcare.my/api/orders/552" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json"
```

### Example Response

```json
{
  "data": {
    "id": 552,
    "invoice_code": "INV-2024-002",
    "status": "Draft",
    "is_ready": false,
    "is_submitted_to_lhdn": false,
    "buyer": {
      "name": "Customer Company Sdn Bhd",
      "tin": "C12345678901",
      "address": "123 Business Street",
      "city": "Kuala Lumpur",
      "state": "Selangor",
      "zip_code": "50000",
      "country": "Malaysia"
    },
    "supplier": {
      "name": "Your Company Sdn Bhd",
      "tin": "C98765432109"
    },
    "line_items": [
      {
        "description": "Consulting Services",
        "quantity": 10,
        "unit_price": 150.00,
        "classification_code": "003",
        "unit_code": "HUR",
        "tax_type": "02",
        "tax_rate": 8.00,
        "tax_amount": 120.00,
        "total_amount": 1620.00
      }
    ],
    "legal_monetary_total": {
      "line_extension_amount": 1500.00,
      "tax_exclusive_amount": 1500.00,
      "tax_inclusive_amount": 1620.00,
      "payable_amount": 1620.00
    },
    "submitted_documents": [],
    "created_at": "2024-01-15T11:00:00Z",
    "updated_at": "2024-01-15T11:00:00Z"
  }
}
```

### Response Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 401 | Unauthorized |
| 404 | Order not found |
| 500 | Internal server error |

---

## Update Order

Update an existing order. Only draft orders that haven't been submitted can be updated.

```http
PUT /api/orders/{id}
```

### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | integer | Yes | Order ID |

### Example Request

```bash
curl -X PUT "https://api.myinvois.bizcare.my/api/orders/552" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "buyer": {
      "name": "Updated Customer Company Sdn Bhd",
      "tin": "C12345678901"
    },
    "line_items": [
      {
        "description": "Updated Consulting Services",
        "quantity": 15,
        "unit_price": 150.00,
        "total_amount": 2430.00
      }
    ]
  }'
```

### Example Response

```json
{
  "success": true,
  "data": {
    "id": 552,
    "invoice_code": "INV-2024-002",
    "status": "Draft",
    "updated_at": "2024-01-15T12:00:00Z"
  }
}
```

### Response Codes

| Code | Description |
|------|-------------|
| 200 | Order updated successfully |
| 400 | Bad request |
| 401 | Unauthorized |
| 403 | Order cannot be updated (already submitted) |
| 404 | Order not found |
| 409 | Invoice code already exists |
| 422 | Validation error |
| 500 | Internal server error |

---

## Delete Order

Delete an existing order. Only draft orders that haven't been submitted can be deleted.

```http
DELETE /api/orders/{id}
```

### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | integer | Yes | Order ID |

### Example Request

```bash
curl -X DELETE "https://api.myinvois.bizcare.my/api/orders/552" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json"
```

### Example Response

```json
{
  "message": "Order deleted successfully"
}
```

### Response Codes

| Code | Description |
|------|-------------|
| 200 | Order deleted successfully |
| 401 | Unauthorized |
| 403 | Order cannot be deleted (already submitted) |
| 404 | Order not found |
| 500 | Internal server error |

---

## Submit Order to MyInvois

Submit a draft order to MyInvois for validation and processing.

```http
POST /api/orders/{id}/submit
```

### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | integer | Yes | Order ID |

### Example Request

```bash
curl -X POST "https://api.myinvois.bizcare.my/api/orders/552/submit" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{}'
```

### Example Response

```json
{
  "success": true,
  "data": {
    "id": 552,
    "status": "Submitted",
    "submitted_document": {
      "id": 153,
      "uuid": "550e8400-e29b-41d4-a716-************",
      "status": "Submitted",
      "type": "Invoice",
      "created_at": "2024-01-15T13:00:00Z"
    }
  }
}
```

### Response Codes

| Code | Description |
|------|-------------|
| 200 | Order submitted successfully |
| 400 | Bad request |
| 401 | Unauthorized |
| 403 | Order cannot be submitted |
| 404 | Order not found |
| 500 | Internal server error |

---

## Validate Invoice Code

Check if an invoice code is unique within your company's orders.

```http
POST /api/orders/validate
```

### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `invoice_code` | string | Yes | Invoice code to validate |

### Example Request

```bash
curl -X POST "https://api.myinvois.bizcare.my/api/orders/validate?invoice_code=INV-2024-003" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json"
```

### Example Response

```json
{
  "data": {
    "success": true
  }
}
```

### Response Codes

| Code | Description |
|------|-------------|
| 200 | Validation result returned |
| 400 | Bad request |
| 401 | Unauthorized |
| 404 | Company not found |
| 500 | Internal server error |

---

## Submit Consolidated Invoices

Submit multiple orders to MyInvois in a single consolidated batch.

```http
POST /api/submit-consolidated-invoices
```

### Request Body

```json
{
  "order_ids": [552, 553, 554],
  "submission_type": "consolidated"
}
```

### Example Request

```bash
curl -X POST "https://api.myinvois.bizcare.my/api/submit-consolidated-invoices" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "order_ids": [552, 553, 554],
    "submission_type": "consolidated"
  }'
```

### Example Response

```json
{
  "data": {
    "success": true,
    "message": "3 orders submitted successfully in consolidated batch"
  }
}
```

### Response Codes

| Code | Description |
|------|-------------|
| 200 | Consolidated submission successful |
| 400 | Bad request |
| 401 | Unauthorized |
| 500 | Internal server error |

## Important Notes

<Callout type="info">
  **Order Status**: Orders must be in "Draft" status to be updated or deleted. Once submitted, orders cannot be modified.
</Callout>

<Callout type="warning">
  **Invoice Code Uniqueness**: Invoice codes must be unique within your company. Use the validation endpoint to check before creating orders.
</Callout>

<Callout type="tip">
  **Consolidated Submissions**: Use consolidated submissions for better performance when submitting multiple orders at once.
</Callout>
