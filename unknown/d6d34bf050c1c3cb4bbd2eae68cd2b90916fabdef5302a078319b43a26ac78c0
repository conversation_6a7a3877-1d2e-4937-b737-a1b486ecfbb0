import { env } from '@/env';
import { log } from '@repo/observability/log';

// API types to distinguish between different API endpoints
export enum ApiType {
  LICENSE = 'license',
  CORE = 'core',
}

/**
 * URL patterns to automatically determine the API type
 */
export const API_PATTERNS = {
  CORE: [
    '/app/v1/', // Core API version 1 endpoints
  ],
  LICENSE: [
    '/api/users', // User management endpoints
    '/api/organizations', // Organization management endpoints
    '/api/auth/', // Authentication endpoints
    '/api/upload', // File upload endpoints
  ],
};

/**
 * Enum for API endpoints
 */
export const ApiEndpoint = {
  // License API endpoints
  USERS: '/api/users',
  ORGANIZATIONS: '/api/organizations',
  UPLOAD: '/api/upload',

  // Core API endpoints
  ORDERS: '/app/v1/orders',
  ORDERS_WITH_DOCUMENT: '/app/v1/orders/with-document',
  SUBMITTED_DOCUMENTS: '/app/v1/submitted-documents',
  ME_COMPANIES: '/app/v1/me/companies',
  COMPANIES: '/app/v1/companies',

  INVOICE_CODES: '/app/v1/invoice-codes/validate',
  TIN_VALIDATION: '/app/v1/tin-validation',
};

/**
 * Determines the API type based on the URL path
 * @param path The URL path to check
 * @returns The determined API type
 */
export const getApiTypeFromPath = (path: string): ApiType => {
  // If the path is a full URL, extract just the path part
  const pathOnly = path.startsWith('http') ? new URL(path).pathname : path;

  // Check if the path matches any of the core API patterns
  if (API_PATTERNS.CORE.some((pattern) => pathOnly.includes(pattern))) {
    return ApiType.CORE;
  }

  // Check if the path matches any of the license API patterns
  if (API_PATTERNS.LICENSE.some((pattern) => pathOnly.includes(pattern))) {
    return ApiType.LICENSE;
  }

  // Default to LICENSE API if no pattern matches
  return ApiType.LICENSE;
};

/**
 * Determines the base URL to use based on the API type
 * @param apiType The type of API to use
 * @returns The base URL for the specified API type
 */
export const getBaseUrl = (apiType: ApiType = ApiType.LICENSE): string => {
  let baseUrl: string;

  switch (apiType) {
    case ApiType.CORE:
      baseUrl = env.NEXT_PUBLIC_CORE_BACKEND_URL;
      break;
    case ApiType.LICENSE:
      baseUrl = env.NEXT_PUBLIC_API_URL;
      break;
    default:
      baseUrl = env.NEXT_PUBLIC_API_URL;
      break;
  }

  return baseUrl;
};

/**
 * Creates a full URL for the specified API type and path
 */
export const createApiUrl = (path: string, apiType?: ApiType): string => {
  // If the path already starts with http, return it as is
  if (path.startsWith('http')) {
    return path;
  }

  // Determine API type from the path if not explicitly provided
  const determinedApiType = apiType || getApiTypeFromPath(path);

  // Make sure the path starts with a slash
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  const fullUrl = getBaseUrl(determinedApiType) + normalizedPath;

  return fullUrl;
};

/**
 * Fetches data from the specified API with JWT authentication
 */
export const fetchWithAuth = async <T>(
  path: string,
  [bearerToken, jwt]: [string | null, string | null],
  options: RequestInit = {}
): Promise<T> => {
  // Determine API type from the path if not explicitly provided
  const determinedApiType = getApiTypeFromPath(path);
  const url = createApiUrl(path, determinedApiType);

  const token = determinedApiType === ApiType.LICENSE ? bearerToken : jwt;

  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      log.warn(`Failed to fetch from ${determinedApiType} API`, {
        status: response.status,
        path,
        url,
      });

      const errorData = await response.json().catch(() => ({}));
      log.warn(`API Error: ${path}`, { errorData });

      throw errorData;
    }

    return await response.json();
  } catch (error) {
    log.warn('fetchWithAuth exception', { error, path, url });
    throw error;
  }
};
