---
title: Companies API
description: Manage company information and tax settings
---

# Companies API

The Companies API allows you to manage your company information, tax settings, and MyInvois integration credentials. This is essential for setting up your e-invoice system and ensuring compliance with Malaysian tax requirements.

## Base Endpoint

```
/api/companies
```

## Company Information

Your company information is used for:
- **Supplier Details**: Automatically populated in all invoices
- **Tax Registration**: SST, Service Tax, and Tourism Tax registration numbers
- **MyInvois Integration**: Client credentials for API access
- **Business Classification**: MSIC codes and business activity descriptions

## Endpoints Overview

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/companies` | Get your company information |
| PUT | `/companies` | Update your company information |
| POST | `/tin-validation` | Validate TIN numbers |

---

## Get Company Information

Retrieve your company's current information and settings.

```http
GET /api/companies
```

### Example Request

```bash
curl -X GET "https://api.myinvois.bizcare.my/api/companies" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json"
```

### Example Response

```json
{
  "data": {
    "id": 173,
    "user_id": 253,
    "name": "Your Company Sdn Bhd",
    "tin_code": "C***********",
    "registration_number": "************",
    "registration_type": "BRN",
    "sst_registration_number": "A12-3456-********",
    "tourism_tax_registration_number": "TTX-*********",
    "business_activity_description": "Software Development and IT Consulting",
    "msic_code": "62010",
    "address": "123 Technology Park",
    "city": "Cyberjaya",
    "state": "Selangor",
    "zip_code": "63000",
    "country": "Malaysia",
    "phone": "+60*********",
    "email": "<EMAIL>",
    "bank_account": "*********0",
    "applicable_tax_types": ["01", "02"],
    "sales_tax_rates": ["5%", "10%"],
    "service_tax_rates": ["6%", "8%"],
    "client_id": "your-myinvois-client-id",
    "client_secret": "your-myinvois-client-secret",
    "scope": "InvoicingAPI",
    "access_token": "current-access-token",
    "token_expires_at": "2024-01-15T18:00:00Z",
    "is_company_ready": true,
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-15T14:00:00Z"
  }
}
```

### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | integer | Company ID |
| `name` | string | Company name |
| `tin_code` | string | Tax Identification Number |
| `registration_number` | string | Business registration number |
| `registration_type` | string | Type of registration (BRN, etc.) |
| `sst_registration_number` | string | Sales and Service Tax registration |
| `tourism_tax_registration_number` | string | Tourism tax registration |
| `business_activity_description` | string | Description of business activities |
| `msic_code` | string | Malaysian Standard Industrial Classification code |
| `applicable_tax_types` | array | Tax types applicable to your business |
| `sales_tax_rates` | array | Sales tax rates you use |
| `service_tax_rates` | array | Service tax rates you use |
| `client_id` | string | MyInvois API client ID |
| `client_secret` | string | MyInvois API client secret |
| `is_company_ready` | boolean | Whether company setup is complete |

### Tax Types

| Code | Description |
|------|-------------|
| `01` | Sales Tax |
| `02` | Service Tax |
| `03` | Tourism Tax |
| `04` | High-Value Goods Tax |
| `05` | Sales Tax on Low Value Goods |
| `06` | Not Applicable |
| `E` | Tax Exemption |

### Response Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 401 | Unauthorized |
| 404 | Company not found |
| 500 | Internal server error |

---

## Update Company Information

Update your company's information and settings.

```http
PUT /api/companies
```

### Request Body

You can update any of the company fields. Only include the fields you want to change.

### Example Request

```bash
curl -X PUT "https://api.myinvois.bizcare.my/api/companies" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated Company Name Sdn Bhd",
    "address": "456 New Business Street",
    "phone": "+***********",
    "applicable_tax_types": ["01", "02", "03"],
    "sales_tax_rates": ["5%", "10%"],
    "service_tax_rates": ["6%", "8%"],
    "business_activity_description": "Updated business description"
  }'
```

### Example Response

```json
{
  "success": true,
  "data": {
    "id": 173,
    "name": "Updated Company Name Sdn Bhd",
    "updated_at": "2024-01-15T15:00:00Z"
  }
}
```

### Response Codes

| Code | Description |
|------|-------------|
| 204 | Company updated successfully |
| 400 | Bad request |
| 401 | Unauthorized |
| 404 | Company not found |
| 422 | Validation error |
| 500 | Internal server error |

---

## Validate TIN

Validate a Tax Identification Number (TIN) with the Malaysian tax authorities.

```http
POST /api/tin-validation
```

### Request Body

```json
{
  "tin": "C***********",
  "id_type": "TIN",
  "id_value": "***********"
}
```

### Example Request

```bash
curl -X POST "https://api.myinvois.bizcare.my/api/tin-validation" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "tin": "C***********",
    "id_type": "TIN",
    "id_value": "***********"
  }'
```

### Example Response

```json
{
  "data": {
    "valid": true
  }
}
```

### Response Codes

| Code | Description |
|------|-------------|
| 200 | Validation result returned |
| 400 | Bad request or invalid TIN format |
| 401 | Unauthorized |
| 500 | Internal server error |

## Company Setup Requirements

To ensure your company is ready for e-invoicing, make sure you have:

### Required Information
- ✅ **Company Name**: Legal business name
- ✅ **TIN Code**: Valid Tax Identification Number
- ✅ **Registration Number**: Business registration number
- ✅ **Complete Address**: Including city, state, and zip code
- ✅ **Contact Information**: Phone and email

### Tax Registration
- ✅ **SST Registration**: If applicable to your business
- ✅ **Tourism Tax Registration**: For tourism-related businesses
- ✅ **Tax Types**: Select applicable tax types for your business
- ✅ **Tax Rates**: Configure the tax rates you use

### MyInvois Integration
- ✅ **Client ID**: From MyInvois developer portal
- ✅ **Client Secret**: From MyInvois developer portal
- ✅ **API Scope**: Usually "InvoicingAPI"

### Business Classification
- ✅ **MSIC Code**: Malaysian Standard Industrial Classification
- ✅ **Business Activity**: Description of your business activities

## Important Notes

<Callout type="info">
  **Company Readiness**: The `is_company_ready` field indicates if your company setup is complete for e-invoicing.
</Callout>

<Callout type="warning">
  **TIN Validation**: Always validate TIN numbers before using them in invoices to avoid submission errors.
</Callout>

<Callout type="tip">
  **Tax Configuration**: Properly configure your applicable tax types and rates to ensure accurate invoice calculations.
</Callout>

## Malaysian Tax Rates Reference

### Sales Tax
- **5%**: Most goods and services
- **10%**: Specific categories as defined by Royal Malaysian Customs

### Service Tax
- **8%**: Most services (effective March 2024)
- **6%**: Food & beverage, telecommunication, parking, logistics

### Tourism Tax
- **RM 10**: Per room per night for non-Malaysian guests
