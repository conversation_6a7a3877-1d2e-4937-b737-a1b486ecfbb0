// @ts-nocheck -- skip type checking
import * as docs_6 from "../content/api/orders.mdx?collection=docs&hash=1750836438168"
import * as docs_5 from "../content/api/models.mdx?collection=docs&hash=1750836438168"
import * as docs_4 from "../content/api/examples.mdx?collection=docs&hash=1750836438168"
import * as docs_3 from "../content/api/errors.mdx?collection=docs&hash=1750836438168"
import * as docs_2 from "../content/api/companies.mdx?collection=docs&hash=1750836438168"
import * as docs_1 from "../content/api/authentication.mdx?collection=docs&hash=1750836438168"
import * as docs_0 from "../content/index.mdx?collection=docs&hash=1750836438168"
import { _runtime } from "fumadocs-mdx"
import * as _source from "../source.config"
export const docs = _runtime.docs<typeof _source.docs>([{ info: {"path":"index.mdx","absolutePath":"/Users/<USER>/development/digital_invoice/einvoice_app/apps/docs/content/index.mdx"}, data: docs_0 }, { info: {"path":"api/authentication.mdx","absolutePath":"/Users/<USER>/development/digital_invoice/einvoice_app/apps/docs/content/api/authentication.mdx"}, data: docs_1 }, { info: {"path":"api/companies.mdx","absolutePath":"/Users/<USER>/development/digital_invoice/einvoice_app/apps/docs/content/api/companies.mdx"}, data: docs_2 }, { info: {"path":"api/errors.mdx","absolutePath":"/Users/<USER>/development/digital_invoice/einvoice_app/apps/docs/content/api/errors.mdx"}, data: docs_3 }, { info: {"path":"api/examples.mdx","absolutePath":"/Users/<USER>/development/digital_invoice/einvoice_app/apps/docs/content/api/examples.mdx"}, data: docs_4 }, { info: {"path":"api/models.mdx","absolutePath":"/Users/<USER>/development/digital_invoice/einvoice_app/apps/docs/content/api/models.mdx"}, data: docs_5 }, { info: {"path":"api/orders.mdx","absolutePath":"/Users/<USER>/development/digital_invoice/einvoice_app/apps/docs/content/api/orders.mdx"}, data: docs_6 }], [{"info":{"path":"meta.json","absolutePath":"/Users/<USER>/development/digital_invoice/einvoice_app/apps/docs/content/meta.json"},"data":{"title":"BizCare MyInvois Documentation","pages":["index","api/authentication","api/orders","api/companies","api/models","api/errors","api/examples"],"root":true}}, {"info":{"path":"api/meta.json","absolutePath":"/Users/<USER>/development/digital_invoice/einvoice_app/apps/docs/content/api/meta.json"},"data":{"title":"API Reference","pages":["authentication","orders","companies","models","errors","examples"]}}])