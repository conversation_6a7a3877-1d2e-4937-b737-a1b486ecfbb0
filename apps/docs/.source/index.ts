// @ts-nocheck -- skip type checking
import * as docs_1 from "../content/docs/test.mdx?collection=docs&hash=1750825543123"
import * as docs_0 from "../content/docs/index.mdx?collection=docs&hash=1750825543123"
import { _runtime } from "fumadocs-mdx"
import * as _source from "../source.config"
export const docs = _runtime.docs<typeof _source.docs>([{ info: {"path":"index.mdx","absolutePath":"/Users/<USER>/development/digital_invoice/einvoice_app/apps/docs/content/docs/index.mdx"}, data: docs_0 }, { info: {"path":"test.mdx","absolutePath":"/Users/<USER>/development/digital_invoice/einvoice_app/apps/docs/content/docs/test.mdx"}, data: docs_1 }], [])