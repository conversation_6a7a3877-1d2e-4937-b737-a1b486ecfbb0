import { fetchWithAuthForProxy, withApi<PERSON>eyAuth } from '@/app/lib/auth';
import { type NextRequest, NextResponse } from 'next/server';

const postHandler = async (request: NextRequest) => {
  const { data, status } = await fetchWithAuthForProxy(
    '/api/v1/submit-consolidated-invoices',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    },
    request.headers
  );

  return NextResponse.json(data, {
    status,
  });
};

export const OPTIONS = withApiKeyAuth(postHandler);
export const POST = withApi<PERSON>eyAuth(postHandler);
