import {
  Form,
  FormControl,
  FormField,
  FormLabel,
  FormMessage,
  GridFormItem,
  useForm,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';

import { Button } from '@repo/design-system/components/ui/button';
import { useUpdateOrder } from '../../hooks/useUpdateOrder';
import type { UpdateBuyerData } from '../../schemas/order-form-schema';

import { ApiEndpoint } from '@/app/lib/api';
import { clientFetchWithAuth } from '@/app/lib/api.client';
import { RegistrationTypes } from '@/app/types';
import { useJwtToken } from '@/app/utils/auth-client-helpers';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { toast } from '@repo/design-system/components/ui/sonner';
import { log } from '@repo/observability/log';
import { type Dispatch, type SetStateAction, useState } from 'react';

interface UpdateUserFormProps {
  initialData: UpdateBuyerData;
  orderId: number;
  setUpdateBuyerOpen: Dispatch<SetStateAction<boolean>>;
}

export function UpdateBuyerForm({
  initialData,
  orderId,
  setUpdateBuyerOpen,
}: UpdateUserFormProps) {
  const { jwt } = useJwtToken();
  const form = useForm<UpdateBuyerData>({
    defaultValues: {
      ...initialData,
    },
  });

  const { updateBuyer, isLoading } = useUpdateOrder();

  const [isVerifying, setIsVerifying] = useState(false);
  const [tinIsVerified, setTinIsVerified] = useState(false);
  const validateTinIsReady =
    form.watch('buyerTin') != undefined &&
    form.watch('registrationNumber') != undefined &&
    form.watch('registrationType') != undefined &&
    form.watch('buyerTin') != '' &&
    form.watch('registrationNumber') != '';
  const onSubmit = async (data: UpdateBuyerData) => {
    if (!jwt) {
      return;
    }

    try {
      await updateBuyer(
        {
          address: data.address
            ? {
                ...data.address,
              }
            : undefined,
          buyerName: data.buyerName ?? undefined,
          buyerTin: data.buyerTin ?? undefined,
          registrationType: data.registrationType ?? undefined,
          registrationNumber: data.registrationNumber ?? undefined,
          sstRegistrationNumber: data.sstRegistrationNumber ?? undefined,
          contactNumber: data.contactNumber ?? undefined,
          email: data.email ?? undefined,
        },
        orderId
      );
      setUpdateBuyerOpen(false);
      toast.success('Buyer updated successfully.');
    } catch (err) {
      toast.error('Failed to update buyer. Please try again.');
    }
  };

  const verifyTaxId = async () => {
    const taxId = form.getValues('buyerTin');
    if (!taxId) {
      form.setError('buyerTin', {
        type: 'manual',
        message: 'Please enter a Tax Identification Number',
      });
      return;
    }

    setIsVerifying(true);

    try {
      form.clearErrors();
      if (!jwt) {
        return;
      }

      const response = await clientFetchWithAuth<{
        data: { valid: boolean };
      }>(ApiEndpoint.TIN_VALIDATION, jwt, {
        method: 'POST',
        body: JSON.stringify({
          tin: form.getValues('buyerTin'),
          registrationType: form.getValues('registrationType'),
          registrationNumber: form.getValues('registrationNumber'),
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.data.valid === false) {
        form.setError('registrationNumber', {
          type: 'manual',
          message: 'Failed to validate TIN. Please try again.',
        });
        form.setError('buyerTin', {
          type: 'manual',
          message: 'Failed to validate TIN. Please try again.',
        });
        throw new Error('Failed to validate TIN');
      }

      setTinIsVerified(true);
      toast.success('TIN verified successfully');
    } catch (error) {
      log.warn('Error verifying tax ID', { error });
      toast.error('Failed to verify TIN. Please try again.');
      setTinIsVerified(false);
    } finally {
      setIsVerifying(false);
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="mx-auto w-full max-w-3xl space-y-4 px-4 md:px-0"
      >
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <FormField
            control={form.control}
            name="buyerName"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Buyer Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter buyer name" {...field} />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <FormField
            control={form.control}
            name="registrationType"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Registration Type</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select registration type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.keys(RegistrationTypes).map((type) => {
                      return <SelectItem value={type}>{type}</SelectItem>;
                    })}
                  </SelectContent>
                </Select>
                <FormMessage />
              </GridFormItem>
            )}
          />

          <FormField
            control={form.control}
            name="registrationNumber"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Registration Number</FormLabel>
                <FormControl>
                  <Input placeholder="Enter buyer TIN" {...field} />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />

          <FormField
            control={form.control}
            name="buyerTin"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Buyer TIN</FormLabel>
                <FormControl>
                  <Input placeholder="Enter buyer TIN" {...field} />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />

          <Button
            type="button"
            onClick={verifyTaxId}
            disabled={isLoading || isVerifying || !validateTinIsReady}
            className="mt-5.5"
          >
            Validate TIN
          </Button>
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <FormField
            control={form.control}
            name="sstRegistrationNumber"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>SST Registration Number</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter SST registration number"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input placeholder="Enter email" {...field} />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <FormField
            control={form.control}
            name="address.addressLine0"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Address Line 0</FormLabel>
                <FormControl>
                  <Input placeholder="Enter address line 0" {...field} />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />

          <FormField
            control={form.control}
            name="address.addressLine1"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Address Line 1</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter address line 1"
                    {...field}
                    value={field.value || ''}
                  />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <FormField
            control={form.control}
            name="address.addressLine2"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Address Line 2</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter address line 2"
                    {...field}
                    value={field.value || ''}
                  />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />

          <FormField
            control={form.control}
            name="address.postalZone"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Postal Zone</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter postal zone"
                    {...field}
                    value={field.value || ''}
                  />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <FormField
            control={form.control}
            name="address.cityName"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>City</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter city"
                    {...field}
                    value={field.value || ''}
                  />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />

          <FormField
            control={form.control}
            name="address.state"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>State</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter state"
                    {...field}
                    value={field.value || ''}
                  />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <FormField
            control={form.control}
            name="address.country"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Country</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter country"
                    {...field}
                    value={field.value || ''}
                  />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />
        </div>

        <div className={'flex justify-end'}>
          <Button
            onClick={form.handleSubmit(onSubmit)}
            disabled={isLoading || isVerifying || !tinIsVerified}
          >
            Submit
          </Button>
        </div>
      </form>
    </Form>
  );
}
